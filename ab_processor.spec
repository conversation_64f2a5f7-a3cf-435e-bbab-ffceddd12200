# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['d:\\wo\\ab处理.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=['cv2', 'numpy'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 添加ffmpeg和exiftool文件夹到打包中
a.datas += [('ffmpeg.exe', 'D:\\external_tools\\ffmpeg\\bin\\ffmpeg.exe', 'DATA')]
a.datas += [('ffprobe.exe', 'D:\\external_tools\\ffmpeg\\bin\\ffprobe.exe', 'DATA')]
a.datas += [('exiftool.exe', 'D:\\external_tools\\exiftool\\exiftool.exe', 'DATA')]

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='视频处理工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='D:\\icons\\video_icon.ico',  # 如果有图标文件，可以在这里指定
) 