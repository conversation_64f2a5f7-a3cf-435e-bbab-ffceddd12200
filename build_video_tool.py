import os
import sys
import shutil
import subprocess
import traceback

# 添加用于隐藏控制台窗口的代码
if os.name == 'nt':  # Windows系统
    startupinfo = subprocess.STARTUPINFO()
    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
    startupinfo.wShowWindow = subprocess.SW_HIDE
    # 创建一个全局的subprocess配置
    DEFAULT_SUBPROCESS_PARAMS = {
        'startupinfo': startupinfo,
        'creationflags': subprocess.CREATE_NO_WINDOW,  # 移除DETACHED_PROCESS标志以查看输出
        'shell': False
    }
else:
    DEFAULT_SUBPROCESS_PARAMS = {}

def build_exe():
    """构建可执行文件"""
    try:
        # 确保已安装pyinstaller
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True, **DEFAULT_SUBPROCESS_PARAMS)
        
        # 检查外部工具路径
        ffmpeg_path = "C:/ffmpeg/bin/ffmpeg.exe"
        ffprobe_path = "C:/ffmpeg/bin/ffprobe.exe"
        exiftool_path = "C:/exiftool-13.30_64/exiftool.exe"
        
        # 验证文件是否存在
        if not os.path.exists(ffmpeg_path):
            print(f"错误: 未找到FFmpeg: {ffmpeg_path}")
            return False
        
        if not os.path.exists(ffprobe_path):
            print(f"错误: 未找到FFprobe: {ffprobe_path}")
            return False
        
        if not os.path.exists(exiftool_path):
            print(f"错误: 未找到ExifTool: {exiftool_path}")
            return False
            
        print(f"找到FFmpeg: {ffmpeg_path}")
        print(f"找到FFprobe: {ffprobe_path}")
        print(f"找到ExifTool: {exiftool_path}")
        
        # 创建临时工具目录
        tools_dir = "temp_tools"
        if not os.path.exists(tools_dir):
            os.makedirs(tools_dir)
            
        # 复制工具文件到临时目录
        shutil.copy2(ffmpeg_path, os.path.join(tools_dir, "ffmpeg.exe"))
        shutil.copy2(ffprobe_path, os.path.join(tools_dir, "ffprobe.exe"))
        shutil.copy2(exiftool_path, os.path.join(tools_dir, "exiftool.exe"))
        
        # 使用英文文件名作为临时脚本和spec文件名
        temp_script = "video_processor_temp.py"
        spec_file = "video_processor.spec"
        
        # 构建规范文件内容 - 使用三重引号和正确的转义
        ffmpeg_tool_path = os.path.join(tools_dir, "ffmpeg.exe").replace("\\", "/")
        ffprobe_tool_path = os.path.join(tools_dir, "ffprobe.exe").replace("\\", "/")
        exiftool_tool_path = os.path.join(tools_dir, "exiftool.exe").replace("\\", "/")
        
        spec_content = f"""# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['{temp_script}'],
    pathex=[],
    binaries=[],
    datas=[
        ('{ffmpeg_tool_path}', '.'),
        ('{ffprobe_tool_path}', '.'),
        ('{exiftool_tool_path}', '.'),
    ],
    hiddenimports=['cv2', 'numpy'],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='颜哥ab工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 改为False以隐藏控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 不使用图标
)"""
        
        # 写入spec文件
        with open(spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        # 添加代码来修改原始程序，使其能够使用打包中的外部工具
        patch_code = """
# 添加代码以支持从PyInstaller打包找到外部工具
import os
import sys

def get_resource_path(relative_path):
    try:
        # PyInstaller创建的临时文件夹
        base_path = sys._MEIPASS
    except Exception:
        # 不是打包时，在当前文件夹中找
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

# 将打包的工具路径添加到环境变量中
if getattr(sys, 'frozen', False):
    os.environ["PATH"] = f"{os.path.dirname(get_resource_path('ffmpeg.exe'))};{os.environ.get('PATH', '')}"
"""
        
        # 创建临时副本以避免修改原始文件
        print("正在创建临时文件副本...")
        # 使用脚本自身的目录来定位ab处理.py
        script_dir = os.path.dirname(os.path.abspath(__file__))
        ab_file_path = os.path.join(script_dir, 'ab处理.py')
        print(f"尝试读取文件: {ab_file_path}")
        with open(ab_file_path, 'r', encoding='utf-8') as f:
            original_code = f.read()
            
        with open(temp_script, 'w', encoding='utf-8') as f:
            # 在导入模块后添加我们的补丁代码
            patch_position = original_code.find('# 移除OpenCV导入，代码中未使用')
            if patch_position != -1:
                # 找到注释的位置，放在这个注释前插入补丁代码
                f.write(original_code[:patch_position] + '\n' + patch_code + original_code[patch_position:])
            else:
                # 如果找不到注释，则尝试在第10行左右插入（这应该是在所有导入语句之后）
                lines = original_code.split('\n')
                insert_position = min(10, len(lines))
                lines.insert(insert_position, patch_code)
                f.write('\n'.join(lines))
        
        # 运行PyInstaller，输出完整信息
        print("正在构建可执行文件...")
        result = subprocess.run(['pyinstaller', '--clean', spec_file], 
                                stdout=subprocess.PIPE, 
                                stderr=subprocess.PIPE, 
                                text=True,
                                encoding='utf-8',
                                errors='replace')
        
        # 输出PyInstaller的输出信息
        print("\nPyInstaller输出:")
        print(result.stdout)
        
        if result.returncode != 0:
            print("\nPyInstaller错误:")
            print(result.stderr)
            return False
        
        # 创建发布文件夹
        release_dir = "颜哥ab工具"
        if os.path.exists(release_dir):
            shutil.rmtree(release_dir)
        os.makedirs(release_dir)
        
        # 复制必要文件到发布文件夹
        shutil.copy2(os.path.join("dist", "颜哥ab工具.exe"), release_dir)
        
        print("\n构建完成！")
        print("生成的文件:")
        print(f"{release_dir}/颜哥ab工具.exe - 独立可执行文件")
        
    except Exception as e:
        print(f"构建过程出错: {str(e)}")
        traceback.print_exc()
        return False
        
    finally:
        # 清理临时文件
        print("清理临时文件...")
        for file in [temp_script, spec_file]:
            if os.path.exists(file):
                try:
                    os.remove(file)
                except Exception as e:
                    print(f"无法删除临时文件 {file}: {str(e)}")
                
        if os.path.exists(tools_dir):
            try:
                shutil.rmtree(tools_dir)
            except Exception as e:
                print(f"无法删除临时目录 {tools_dir}: {str(e)}")
    
    return True

if __name__ == "__main__":
    build_exe() 