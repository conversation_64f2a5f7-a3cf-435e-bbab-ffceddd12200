[2025-07-29 13:30:15] 错误: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x00007FF7B86B5E05+2814805]
	GetHandleVerifier [0x00007FF7B84196F0+76864]
	(No symbol) [0x00007FF7B81D901A]
	(No symbol) [0x00007FF7B81B1B91]
	(No symbol) [0x00007FF7B825F27E]
	(No symbol) [0x00007FF7B827F2B2]
	(No symbol) [0x00007FF7B8257793]
	(No symbol) [0x00007FF7B822071D]
	(No symbol) [0x00007FF7B82214B3]
	GetHandleVerifier [0x00007FF7B86E0C2D+2990461]
	GetHandleVerifier [0x00007FF7B86DB1B2+2967298]
	GetHandleVerifier [0x00007FF7B86F8713+3087459]
	GetHandleVerifier [0x00007FF7B8433ADA+184362]
	GetHandleVerifier [0x00007FF7B843B84F+216479]
	GetHandleVerifier [0x00007FF7B8421A94+110564]
	GetHandleVerifier [0x00007FF7B8421C42+110994]
	GetHandleVerifier [0x00007FF7B8407FC9+5401]
	BaseThreadInitThunk [0x00007FFA92BE259D+29]
	RtlUserThreadStart [0x00007FFA9488AF78+40]

Traceback (most recent call last):
  File "d:\wo\kuaishou.py", line 4564, in run
    self.driver.get(self.creator_url)  # 重新加载页面
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\undetected_chromedriver\__init__.py", line 665, in get
    return super().get(url)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\selenium\webdriver\remote\webdriver.py", line 472, in get
    self.execute(Command.GET, {"url": url})
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\selenium\webdriver\remote\webdriver.py", line 447, in execute
    self.error_handler.check_response(response)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\selenium\webdriver\remote\errorhandler.py", line 232, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.NoSuchWindowException: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x00007FF7B86B5E05+2814805]
	GetHandleVerifier [0x00007FF7B84196F0+76864]
	(No symbol) [0x00007FF7B81D901A]
	(No symbol) [0x00007FF7B81B1B91]
	(No symbol) [0x00007FF7B825F27E]
	(No symbol) [0x00007FF7B827F2B2]
	(No symbol) [0x00007FF7B8257793]
	(No symbol) [0x00007FF7B822071D]
	(No symbol) [0x00007FF7B82214B3]
	GetHandleVerifier [0x00007FF7B86E0C2D+2990461]
	GetHandleVerifier [0x00007FF7B86DB1B2+2967298]
	GetHandleVerifier [0x00007FF7B86F8713+3087459]
	GetHandleVerifier [0x00007FF7B8433ADA+184362]
	GetHandleVerifier [0x00007FF7B843B84F+216479]
	GetHandleVerifier [0x00007FF7B8421A94+110564]
	GetHandleVerifier [0x00007FF7B8421C42+110994]
	GetHandleVerifier [0x00007FF7B8407FC9+5401]
	BaseThreadInitThunk [0x00007FFA92BE259D+29]
	RtlUserThreadStart [0x00007FFA9488AF78+40]

