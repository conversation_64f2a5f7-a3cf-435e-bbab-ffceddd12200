import os
import sys
import shutil
import subprocess

# 添加用于隐藏控制台窗口的代码
if os.name == 'nt':  # Windows系统
    startupinfo = subprocess.STARTUPINFO()
    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
    startupinfo.wShowWindow = subprocess.SW_HIDE
else:
    startupinfo = None

def build_exe():
    """构建可执行文件"""
    try:
        # 确保已安装pyinstaller
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True, startupinfo=startupinfo)
        
        # 检查本地FFmpeg
        ffmpeg_path = shutil.which('ffmpeg')
        ffprobe_path = shutil.which('ffprobe')
        
        if not ffmpeg_path or not ffprobe_path:
            print("错误: 未找到本地FFmpeg或FFprobe。请确保FFmpeg已添加到系统环境变量中。")
            return False
            
        print(f"找到本地FFmpeg: {ffmpeg_path}")
        print(f"找到本地FFprobe: {ffprobe_path}")
        
        # 创建ffmpeg目录并复制文件
        ffmpeg_dir = "ffmpeg"
        if not os.path.exists(ffmpeg_dir):
            os.makedirs(ffmpeg_dir)
            
        # 复制FFmpeg文件到打包目录
        shutil.copy2(ffmpeg_path, os.path.join(ffmpeg_dir, "ffmpeg.exe"))
        shutil.copy2(ffprobe_path, os.path.join(ffmpeg_dir, "ffprobe.exe"))
        
        # 构建规范文件内容
        spec_content = r"""# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['ytredssfd.py'],
    pathex=[],
    binaries=[
        ('ffmpeg/ffmpeg.exe', 'ffmpeg'),
        ('ffmpeg/ffprobe.exe', 'ffmpeg'),
    ],
    datas=[],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='猴哥出品',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=r'C:\Users\<USER>\PycharmProjects\PythonProject1\xuex\app.ico'
)"""
        
        # 写入spec文件
        with open('ytredssfd.spec', 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        # 运行PyInstaller
        print("正在构建可执行文件...")
        subprocess.run(['pyinstaller', '--clean', 'ytredssfd.spec'], check=True, startupinfo=startupinfo)
        
        # 创建发布文件夹
        release_dir = "VideoProcessor_Release"
        if os.path.exists(release_dir):
            shutil.rmtree(release_dir)
        os.makedirs(release_dir)
        
        # 复制必要文件到发布文件夹
        shutil.copy2(os.path.join("dist", "猴哥出品.exe"), release_dir)
        
        # 创建ZIP文件
        print("正在创建ZIP文件...")
        shutil.make_archive("猴哥出品", 'zip', release_dir)
        
        print("构建完成！")
        print("生成的文件:")
        print("1. 猴哥出品.zip - 可以直接分发的压缩包")
        print("2. dist/猴哥出品.exe - 独立可执行文件")
        
    except Exception as e:
        print(f"构建过程出错: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    build_exe() 